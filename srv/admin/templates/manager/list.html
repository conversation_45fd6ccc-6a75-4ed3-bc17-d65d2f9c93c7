{% extends "base.html" %}

{% block content %}
  <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h2 class="h2">成员管理</h2>
    {% if cur_adm.can_create_roles %}
      <div class="btn-toolbar mb-2 ms-2">
        <a href="/adm/manager/form/"
           class="btn btn-sm btn-outline-primary">
          <i class="fas fa-plus"></i>
          新增管理员
        </a>
      </div>
    {% endif %}
  </div>

  <div class="table-responsive">
    <table class="table table-striped table-hover table-bordered table-sm align-middle">
      <thead>
        <tr>
          <th class="px-3 text-center w-5">ID</th>
          <th class="px-3">用户名</th>
          <th class="px-3">手机号</th>
          <th class="px-3">创建时间</th>
          <th class="px-3 w-20">操作</th>
        </tr>
      </thead>
      <tbody>
        {% for manager in managers %}
          <tr>
            <td class="px-3 text-center">{{ manager.id }}</td>
            <td class="px-3">{{ manager.username }}</td>
            <td class="px-3">{{ manager.phone }}</td>
            <td class="px-3">{{ manager.created.strftime("%Y-%m-%d %H:%M:%S") }}</td>
            <td class="px-3">
              <div class="btn-group">
                {% if cur_adm.can_edit_adm(manager) %}
                  <a href="/adm/manager/form/?manager_id={{ manager.id }}"
                     class="btn btn-sm btn-warning">修改</a>
                {% endif %}
                {% if cur_adm.can_create_role(manager.role) %}
                  <a href="/adm/manager/delete/?manager_id={{ manager.id }}"
                     class="btn btn-sm btn-danger"
                     onclick="return confirm('确定要删除吗？');">删除</a>
                {% endif %}
              </div>
            </td>
          </tr>
        {% endfor %}
      </tbody>
    </table>
  </div>
{% endblock content %}
