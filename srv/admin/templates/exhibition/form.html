{% extends "base.html" %}

{% block content %}
  <h2 class="h2">展览管理</h2>

  <form method="post"
        action="/adm/exhibition/save/"
        enctype="multipart/form-data">
    {% if exhibition %}<input type="hidden" name="eid" value="{{ exhibition.id }}" />{% endif %}
    <input type="hidden"
           name="thumbnail_path"
           value="{{ exhibition.thumbnail }}" />
    <input type="hidden"
           name="banner_path"
           value="{{ exhibition.banner }}" />
    <div class="mb-3 col-md-6">
      <label for="name" class="form-label">展览名称</label>
      <input type="text"
             class="form-control border-primary-subtle"
             id="name"
             name="name"
             value="{{ exhibition.name if exhibition else '' }}"
             required />
    </div>
    <div class="mb-3 col-md-6">
      <label for="thumbnail" class="form-label">缩略图</label>
      <input type="file"
             class="form-control border-primary-subtle"
             id="thumbnail"
             name="thumbnail"
             accept="image/jpeg,image/png"
             required />
    </div>
    <div class="mb-3 col-md-6">
      <label for="banner" class="form-label">概览图</label>
      <input type="file"
             class="form-control border-primary-subtle"
             id="banner"
             name="banner"
             accept="image/jpeg,image/png"
             required />
    </div>
    <div class="mb-3 col-md-6">
      <label for="details" class="form-label">详情图</label>
      <div id="detail-images">
        <input type="file"
               class="form-control border-primary-subtle mb-2"
               name="details"
               accept="image/jpeg,image/png" />
      </div>
      <button type="button"
              class="btn btn-secondary btn-sm"
              onclick="addDetailImage()">+ 添加图片</button>
    </div>
    <script>
      function addDetailImage() {
        const container = document.getElementById('detail-images');
        const input = document.createElement('input');
        input.type = 'file';
        input.className = 'form-control mb-2';
        input.name = 'details';
        input.accept = 'image/jpeg,image/png';
        container.appendChild(input);
      }
    </script>
    <button type="submit" class="btn btn-primary">保存</button>
    <a href="/adm/exhibition/" class="btn btn-secondary">取消</a>
  </form>
{% endblock content %}
