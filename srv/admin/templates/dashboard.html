{% extends "base.html" %}

{% block content %}
  <div class="container-fluid">
    <h2 class="h2">数据概览</h2>
    <p>欢迎来到后台管理系统！</p>

    <div class="row">
      <div class="col-md-3 mb-3">
        <div class="card text-white bg-primary">
          <div class="card-body">
            <h5 class="card-title">用户总数</h5>
            <p class="card-text fs-4">{{ n_user }}</p>
          </div>
        </div>
      </div>
      <div class="col-md-3 mb-3">
        <div class="card text-white bg-danger">
          <div class="card-body">
            <h5 class="card-title">订单总量</h5>
            <p class="card-text fs-4">{{ n_order }}</p>
          </div>
        </div>
      </div>
      <div class="col-md-3 mb-3">
        <div class="card text-white bg-info">
          <div class="card-body">
            <h5 class="card-title">昨日流水</h5>
            <p class="card-text fs-4">{{ income_yesterday }}</p>
          </div>
        </div>
      </div>
      <div class="col-md-3 mb-3">
        <div class="card text-white bg-warning">
          <div class="card-body">
            <h5 class="card-title">本周收入</h5>
            <p class="card-text fs-4">{{ income_this_week }}</p>
          </div>
        </div>
      </div>
    </div>

    <div class="row mt-4">
      <div class="col-md-6 mb-4">
        <div class="card">
          <div class="card-body">
            <h5 class="card-title">活跃时间段</h5>
            <canvas id="hourlyActivityChart"></canvas>
          </div>
        </div>
      </div>
      <div class="col-md-6 mb-4">
        <div class="card">
          <div class="card-body">
            <h5 class="card-title">每日收入趋势</h5>
            <canvas id="dailyIncomeChart"></canvas>
          </div>
        </div>
      </div>
    </div>

    <div class="row">
      <div class="col-md-6 mb-4">
        <div class="card">
          <div class="card-body">
            <h5 class="card-title">展馆收入对比</h5>
            <div style="height: 300px;" class="text-center">
              <canvas id="venueIncomeChart"></canvas>
            </div>
          </div>
        </div>
      </div>
      <div class="col-md-6 mb-4">
        <div class="card">
          <div class="card-body">
            <h5 class="card-title">每周收入</h5>
            <div style="height: 300px;">
              <canvas id="weeklyIncomeChart"></canvas>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="row">
      <div class="col-md-12 mb-4">
        <div class="card">
          <div class="card-body">
            <h5 class="card-title">每月收入</h5>
            <canvas id="monthlyIncomeChart"></canvas>
          </div>
        </div>
      </div>
    </div>
  </div>
{% endblock content %}

{% block ext_js %}
  <script src="https://lf26-cdn-tos.bytecdntp.com/cdn/expire-1-M/Chart.js/3.7.1/chart.min.js"></script>
  <script>
  const chartData = {{ chart_data|safe }};
  const dopamineColors = [
    '#FF4D4D', '#FFC700', '#00C853', '#2979FF', '#D500F9',
    '#FF6D00', '#6200EA', '#00B8D4', '#FFD600', '#FF3D00'
  ];
  const dopamineColorsRGBA = [
    'rgba(255, 77, 77, 0.2)', 'rgba(255, 199, 0, 0.2)', 'rgba(0, 200, 83, 0.2)', 'rgba(41, 121, 255, 0.2)', 'rgba(213, 0, 249, 0.2)',
    'rgba(255, 109, 0, 0.2)', 'rgba(98, 0, 234, 0.2)', 'rgba(0, 184, 212, 0.2)', 'rgba(255, 214, 0, 0.2)', 'rgba(255, 61, 0, 0.2)'
  ];

  // 活跃时间段
  new Chart(document.getElementById('hourlyActivityChart'), {
    type: 'line',
    data: {
      labels: chartData.hourly_activity.labels,
      datasets: [{
        label: '活跃次数',
        data: chartData.hourly_activity.data,
        borderColor: dopamineColors[3],
        backgroundColor: dopamineColorsRGBA[3],
        fill: true,
        tension: 0.1
      }]
    },
    options: {
      scales: {
        y: {
          beginAtZero: true,
          ticks: {
            precision: 0
          }
        }
      }
    }
  });

  // 每日收入趋势
  new Chart(document.getElementById('dailyIncomeChart'), {
    type: 'line',
    data: {
      labels: chartData.daily_income.labels,
      datasets: [{
        label: '每日收入',
        data: chartData.daily_income.data,
        borderColor: dopamineColors[0],
        backgroundColor: dopamineColorsRGBA[0],
        fill: true,
        tension: 0.1
      }]
    }
  });

  // 展馆收入对比
  new Chart(document.getElementById('venueIncomeChart'), {
    type: 'pie',
    data: {
      labels: Object.keys(chartData.venue_income),
      datasets: [{
        data: Object.values(chartData.venue_income),
        backgroundColor: dopamineColors
      }]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false
    }
  });

  // 每周收入
  new Chart(document.getElementById('weeklyIncomeChart'), {
    type: 'bar',
    data: {
      labels: chartData.weekly_income.labels,
      datasets: [{
        label: '周收入',
        data: chartData.weekly_income.data,
        backgroundColor: dopamineColors[4]
      }]
    }
  });

  // 每月收入
  new Chart(document.getElementById('monthlyIncomeChart'), {
    type: 'bar',
    data: {
      labels: chartData.monthly_income.labels,
      datasets: [{
        label: '月收入',
        data: chartData.monthly_income.data,
        backgroundColor: dopamineColors[1]
      }]
    }
  });
  </script>
{% endblock ext_js %}
