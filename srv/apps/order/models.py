import datetime
from functools import cached_property
from secrets import randbelow
from typing import ClassVar

from tortoise import fields
from xxhash import xxh32

import config as cfg
from apps.exhi.models import ExhiHall
from apps.exhi.schemas import TicketCategory as Catg
from apps.order.schemas import OrderStatus
from apps.user.models import User
from libs.attrdict import AttrDict
from libs.orm import Model, relate_cache
from libs.utils import iso_date, up_url


def expire_time():
    """订单过期时间: 30分钟"""
    return datetime.datetime.now(cfg.TZ) + datetime.timedelta(minutes=30)


class Order(Model):
    """订单"""

    uid = fields.IntField(null=False, db_index=True, description='用户 ID')
    tid = fields.IntField(null=False, db_index=True, description='展票 ID')
    hid = fields.IntField(null=False, db_index=True, description='展馆 ID')  # 冗余字段
    phone = fields.CharField(max_length=11, null=False, db_index=True, description='用户手机号')  # 冗余字段
    catg = fields.CharEnumField(Catg, max_length=32, default=Catg.ticket, description='门票类型')
    status = fields.CharEnumField(OrderStatus, max_length=32, default=OrderStatus.pending, description='订单状态')
    quantity = fields.IntField(null=False, description='数量')
    amount = fields.FloatField(null=False, db_index=True, description='订单价格')
    timeslot = fields.CharField(max_length=64, null=False, description='场次')
    grade = fields.CharField(max_length=64, null=False, description='票档')

    # 支付相关字段
    trade_no = fields.CharField(max_length=32, unique=True, description='商户订单号')
    wx_prepay_id = fields.CharField(max_length=64, unique=True, null=True, description='微信预支付ID')
    wx_transaction_id = fields.CharField(max_length=32, unique=True, null=True, description='微信支付交易号')
    payment_time = fields.DatetimeField(null=True, description='支付时间')
    expire_time = fields.DatetimeField(null=True, default=expire_time, description='订单过期时间')

    # 退款相关字段
    refund_no = fields.CharField(max_length=64, unique=True, null=True, description='商户退款单号')
    wx_refund_id = fields.CharField(max_length=32, unique=True, null=True, description='微信退款单号')
    refund_amount = fields.FloatField(default=0.0, description='退款金额')
    refund_reason = fields.CharField(max_length=80, null=True, description='退款原因')
    refund_time = fields.DatetimeField(null=True, description='退款时间')

    vcode = fields.CharField(max_length=64, unique=True, null=True, description='验证码')
    archive: list = fields.JSONField(null=False, description='门票归档信息')  # 订单归档信息  # type: ignore

    ap_time = fields.DatetimeField(null=True, description='预定时间')

    created = fields.DatetimeField(auto_now_add=True, description='创建时间')
    updated = fields.DatetimeField(auto_now=True, description='更新时间')

    # 预声明关联属性
    user: User
    exhihall: ExhiHall

    class Meta:  # type: ignore
        ordering: ClassVar[list[str]] = ['-id']

    @classmethod
    async def create(cls, **kwargs):  # type: ignore
        """创建订单"""
        keys = ['uid', 'tid', 'hid', 'phone', 'catg', 'quantity', 'amount', 'timeslot', 'grade', 'archive']
        params = {k: kwargs[k] for k in keys}

        trade_no = cls.gen_trade_no(params['uid'], params['tid'], params['amount'])
        params['trade_no'] = trade_no

        return await super().create(**params)

    async def delete(self):
        """删除订单"""
        if self.status in [OrderStatus.paid, OrderStatus.refunding]:
            raise ValueError(f'{self.status}的订单无法删除')

        await super().delete()

    @cached_property
    def ticket(self):
        """归档的展票信息"""
        return AttrDict(self.archive)

    @relate_cache
    async def load_user(self):
        return await User.get(id=self.uid)

    @relate_cache
    async def load_exhihall(self):
        return await ExhiHall.get(id=self.hid)

    async def description(self):
        """订单描述"""
        await self.prefetch('ticket')
        desc = f'{self.ticket.title} - {self.grade}{self.timeslot} - {self.quantity} 张 - {self.amount} 元'
        return desc[:127]  # 微信支付限制订单描述长度为 127 字符

    async def detail(self):
        """订单详情"""
        await self.prefetch()
        return {
            'id': self.id,
            'title': self.ticket.title,
            'hall': self.exhihall.name,
            'address': self.exhihall.addr,
            'phone': self.phone,
            'catg': self.catg,
            'status': self.status,
            'quantity': self.quantity,
            'amount': self.amount,
            'timeslot': self.timeslot,
            'grade': self.grade,
            'thumbnail': up_url(self.ticket.thumbnail),
            'start': self.ticket.start,
            'end': self.ticket.end,
            'wx_transaction_id': self.wx_transaction_id,
            'trade_no': self.trade_no,
            'vcode': self.vcode,
            'refund_no': self.refund_no,
            'refund_reason': self.refund_reason,
            'created': iso_date(self.created),
            'expire_time': iso_date(self.expire_time) if self.expire_time else None,
            'payment_time': iso_date(self.payment_time) if self.payment_time else None,
            'refund_time': iso_date(self.refund_time) if self.refund_time else None,
            'ap_time': iso_date(self.ap_time, 'd') if self.ap_time else None,
        }

    @staticmethod
    def gen_trade_no(uid, tid, amount):
        """生成订单号"""
        padding = 16
        now = datetime.datetime.now(cfg.TZ).strftime('%Y%m%d%H%M%S%f')
        info = f'{now}-{uid}-{tid}-{amount}'.encode()
        digest = xxh32(info).intdigest() << padding
        rand = randbelow(2**padding)
        return f'{digest + rand:012X}'

    def is_expired(self) -> bool:
        """检查订单是否过期"""
        if not self.expire_time:
            return False
        return datetime.datetime.now(cfg.TZ) >= self.expire_time

    async def cancel(self):
        """取消订单"""
        if self.status != OrderStatus.pending:
            raise ValueError(f'{self.status}的订单无法取消')

        self.status = OrderStatus.canceled
        await self.save()

    async def pay_success(self, wx_transaction_id: str, success_time: str):
        """支付成功处理"""
        if self.status == OrderStatus.paid:
            return  # 如果已支付，直接返回

        if self.status not in [OrderStatus.pending, OrderStatus.closed]:
            raise ValueError(f'{self.status}的订单无法执行此操作')

        # 修改订单状态，记录微信支付交易号
        self.status = OrderStatus.paid
        self.wx_transaction_id = wx_transaction_id
        self.payment_time = datetime.datetime.fromisoformat(success_time)
        # 生成验证码
        self.vcode = str(self.id * 10000 + int(self.payment_time.timestamp() % 100) * 100 + randbelow(100))
        await self.save()

    async def pay_failed(self):
        """支付失败处理"""
        self.status = OrderStatus.failed
        await self.save()

    async def checkin(self):
        """验票核销"""
        if self.status != OrderStatus.paid:
            raise ValueError(f'{self.status}的订单无法核销')

        self.status = OrderStatus.used
        await self.save()

    async def start_refund(self):
        """开始退款"""
        if self.status != OrderStatus.paid:
            raise ValueError(f'{self.status}的订单无法退款')

        self.status = OrderStatus.refunding
        self.refund_amount = self.amount
        self.refund_reason = '用户申请退款'
        await self.save()

    async def refund_success(self, wx_refund_id: str, success_time: str):
        """退款成功"""
        if self.status == OrderStatus.refunded and self.wx_refund_id == wx_refund_id:
            return  # 幂等性保证

        self.status = OrderStatus.refunded
        self.wx_refund_id = wx_refund_id
        self.refund_time = datetime.datetime.fromisoformat(success_time)
        await self.save()

    async def refund_failed(self, reason: str):
        """退款失败"""
        if self.status != OrderStatus.refunding:
            return  # 如果不是退款中状态，直接返回

        self.status = OrderStatus.paid
        self.refund_reason = reason
        await self.save()

    async def close(self):
        """关闭订单"""
        if self.status != OrderStatus.pending:
            raise ValueError(f'{self.status}的订单无法关闭')

        self.status = OrderStatus.closed
        await self.save()

    @classmethod
    async def close_all_expired_orders(cls):
        """关闭所有过期订单"""
        await cls.filter(status=OrderStatus.pending, expire_time__lte=datetime.datetime.now(cfg.TZ)).update(
            status=OrderStatus.closed
        )

    @classmethod
    async def get_yesterday_income(cls, hid: int | None = None) -> float:
        """获取昨日流水"""
        today_dt = datetime.datetime.now(cfg.TZ)
        today = today_dt.replace(hour=0, minute=0, second=0, microsecond=0)
        yesterday = today - datetime.timedelta(days=1)

        hid_filter = f'AND hid = {hid}' if hid else ''
        query = f"""
            SELECT SUM(amount) as s
            FROM "order"
            WHERE status = '{OrderStatus.paid.value}'
            AND payment_time >= '{yesterday}'
            AND payment_time < '{today}'
            {hid_filter};
        """  # noqa: S608
        result = await cls.aggregate(query)
        return result[0]['s'] if result and result[0]['s'] else 0.0

    @classmethod
    async def get_this_week_income(cls, hid: int | None = None) -> float:
        """获取本周收入"""
        today_dt = datetime.datetime.now(cfg.TZ)
        monday = today_dt - datetime.timedelta(days=today_dt.weekday())
        monday = monday.replace(hour=0, minute=0, second=0, microsecond=0)

        hid_filter = f'AND hid = {hid}' if hid else ''
        query = f"""
            SELECT SUM(amount) as s
            FROM "order"
            WHERE status = '{OrderStatus.paid.value}'
            AND payment_time >= '{monday}'
            {hid_filter};
        """  # noqa: S608
        result = await cls.aggregate(query)
        return result[0]['s'] if result and result[0]['s'] else 0.0

    @classmethod
    async def get_daily_income_trend(cls, days: int = 30, hid: int | None = None):
        """获取每日收入趋势 (最近N天)"""
        hid_filter = f'AND hid = {hid}' if hid else ''
        query = f"""
            SELECT
                DATE_TRUNC('day', payment_time AT TIME ZONE 'UTC' AT TIME ZONE '{cfg.TZ.key}')::date AS day,
                SUM(amount) AS total
            FROM
                "order"
            WHERE
                status = '{OrderStatus.paid.value}' AND payment_time >= NOW() - INTERVAL '{days} days'
                {hid_filter}
            GROUP BY day
            ORDER BY day;
        """  # noqa: S608
        return await cls.aggregate(query)

    @classmethod
    async def get_venue_income(cls):
        """获取各展馆收入对比"""
        query = f"""
            SELECT
                h.name,
                SUM(o.amount) as total
            FROM
                "order" o
            JOIN
                "exhihall" h ON o.hid = h.id
            WHERE
                o.status = '{OrderStatus.paid.value}'
            GROUP BY
                h.name
            HAVING
                SUM(o.amount) > 0
            ORDER BY
                total DESC;
        """  # noqa: S608
        result = await cls.aggregate(query)
        return {item['name']: item['total'] for item in result}

    @classmethod
    async def get_weekly_income_trend(cls, weeks: int = 8, hid: int | None = None):
        """获取每周收入趋势 (最近N周)"""
        hid_filter = f'AND hid = {hid}' if hid else ''
        query = f"""
            SELECT
                DATE_TRUNC('week', payment_time AT TIME ZONE 'UTC' AT TIME ZONE '{cfg.TZ.key}')::date AS week,
                SUM(amount) AS total
            FROM
                "order"
            WHERE
                status = '{OrderStatus.paid.value}' AND payment_time >= NOW() - INTERVAL '{weeks} weeks'
                {hid_filter}
            GROUP BY week
            ORDER BY week;
        """  # noqa: S608
        return await cls.aggregate(query)

    @classmethod
    async def get_monthly_income_trend(cls, months: int = 12, hid: int | None = None):
        """获取每月收入趋势 (最近N个月)"""
        hid_filter = f'AND hid = {hid}' if hid else ''
        query = f"""
            SELECT
                DATE_TRUNC('month', payment_time AT TIME ZONE 'UTC' AT TIME ZONE '{cfg.TZ.key}')::date AS month,
                SUM(amount) AS total
            FROM
                "order"
            WHERE
                status = '{OrderStatus.paid.value}' AND payment_time >= NOW() - INTERVAL '{months} months'
                {hid_filter}
            GROUP BY month
            ORDER BY month;
        """  # noqa: S608
        return await cls.aggregate(query)
