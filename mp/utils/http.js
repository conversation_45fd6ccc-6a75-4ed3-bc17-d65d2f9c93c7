import Cache from './cache';

// 不同类型的缓存过期时间
const API_EXPIR = 300; // 普通缓存 5 分钟
const JWT_EXPIR = 86400; // Json Web Token 缓存 1 天

// API endpoints
const baseUrl = 'https://mp.seamile.cn';
const API = {
  weixinLogin: `${baseUrl}/api/weixin/login`,
  weixinProfile: `${baseUrl}/api/weixin/profile`,
  getUserPhone: `${baseUrl}/api/get/phone`,
  bindUserPhone: `${baseUrl}/api/bind/phone`,
  settings: `${baseUrl}/api/settings`,
  exhiRcmd: `${baseUrl}/api/exhi_rcmd`,
  ticketList: `${baseUrl}/api/ticket/list`,
  ticketDetail: `${baseUrl}/api/ticket/detail`,
  ticketPrice: `${baseUrl}/api/ticket/price`,
  jointList: `${baseUrl}/api/joint/list`,
  jointDetail: `${baseUrl}/api/joint/detail`,
  jointPrice: `${baseUrl}/api/joint/price`,
  reserveList: `${baseUrl}/api/reserve/list`,
  reserveConfirm: `${baseUrl}/api/reserve/confirm`,
  orderSubmit: `${baseUrl}/api/order/submit`,
  continue2Pay: `${baseUrl}/api/order/pay`,
  orderList: `${baseUrl}/api/order/list`,
  orderCheckin: `${baseUrl}/api/order/checkin`,
  orderDetail: `${baseUrl}/api/order/detail`,
  orderCancel: `${baseUrl}/api/order/cancel`,
  orderRefund: `${baseUrl}/api/order/refund`,
  orderDelete: `${baseUrl}/api/order/delete`
};

// 缓存模块
class Jwt {
  static key = 'JsonWebToken';

  // 获取 token
  static async getToken() {
    let token = Cache.get(Jwt.key);
    if (!token) {
      try {
        await login();
        token = Cache.get(Jwt.key);
      } catch (err) {
        wx.showToast({ title: `J: ${err.message || err.message}`, icon: 'none' });
        throw err;
      }
    }
    return token;
  }

  // 保存token
  static saveToken(jwt) {
    Cache.set(Jwt.key, jwt, JWT_EXPIR);
  }

  // 清除token
  static clearToken() {
    Cache.del(Jwt.key);
  }
}

// 登录
function login() {
  return new Promise((resolve, reject) => {
    wx.login({
      success: (res) => {
        wx.request({
          method: 'GET',
          url: API.weixinLogin,
          data: { js_code: res.code },
          enableHttp2: true,
          success: ({ statusCode, data }) => {
            if (statusCode === 200) {
              console.log('Login Success');
              Cache.set('user', {
                openid: data.openid || null,
                name: data.name || null,
                avatar: data.avatar || null,
                phone: data.phone || null,
                isAdm: data.isAdm || null
              });

              Jwt.saveToken(data.jwt); // 登录成功，保存token
              resolve(data); // 登录成功时 resolve
            } else {
              wx.showToast({ title: `S: ${data.msg}`, icon: 'none' });
              reject(new Error(`登录失败，状态码: ${statusCode}`));
            }
          },
          fail: (err) => {
            wx.showToast({ title: `R: ${err.errMsg}`, icon: 'none' });
            reject(new Error(`网络请求失败: ${err.errMsg}`));
          }
        });
      },
      fail: (err) => {
        wx.showToast({ title: `L: ${err.errMsg}`, icon: 'none' });
        reject(new Error(`微信登录失败: ${err.errMsg}`));
      }
    });
  });
}

// 统一的请求处理函数
async function request(url, params = {}, method = 'GET', header = {}, needAuth = true) {
  return new Promise(async (resolve, reject) => {
    // 如果需要认证，添加token到header
    if (needAuth) {
      const token = await Jwt.getToken();
      if (token) {
        header['Authorization'] = `Bearer ${token}`;
      } else {
        wx.showToast({ title: '未登录', icon: 'none' });
        return reject(new Error('未登录'));
      }
    }

    console.log(`${method}: ${url}`);
    wx.request({
      url,
      data: params,
      method: method,
      header,
      success: ({ data, statusCode, header }) => {
        // 处理token过期的情况
        if (statusCode === 401 && data.detail === 'TokenExpired') {
          Jwt.clearToken();
          Jwt.getToken().then(() => {
            request(url, params, method, header, needAuth).then(resolve).catch(reject);
          });
        } else if (statusCode >= 400) {
          const errmsg = data.msg || data.detail || data.errmsg || data.message || `请求失败: ${method} ${url}`;
          wx.showToast({ title: errmsg, icon: 'none' });
          return reject(data);
        }

        // 保存新的token（如果有）
        const newToken = header['RefreshedToken'];
        if (newToken) Jwt.saveToken(newToken);

        resolve(data);
      },
      fail: ({ data }) => {
        const errmsg = data.msg || data.detail || data.errmsg || data.message || `请求失败: ${method} ${url}`;
        wx.showToast({ title: errmsg, icon: 'error' });
        return reject(data);
      }
    });
  });
}

// 获取用户头像昵称
async function getProfile(encryptedData, iv) {
  let user = Cache.get('user', {});
  if (!user.avatar) {
    const profile = await request(API.weixinProfile, { encrypted: encryptedData, iv: iv });
    user = { ...user, ...profile };
    Cache.set('user', user);
  }
  return user;
}

// 绑定手机号
async function getUserPhone(encrypted, iv, code) {
  const user = Cache.get('user', {});
  if (user && user.phone) {
    return user.phone;
  } else {
    const res = await request(API.getUserPhone, { encrypted, iv, code });
    user.phone = res.phone;
    return res.phone;
  }
}

// 绑定手机号
async function bindUserPhone(phone) {
  const res = await request(API.bindUserPhone, { phone }, 'POST');
  if (res.msg === 'OK') {
    wx.showToast({ title: '绑定成功！', icon: 'success' });
    const user = Cache.get('user', {});
    user.phone = phone;
    Cache.set('user', user);
    return true;
  }
}

// 获取系统设置
async function getSettings(ignore_cache = false) {
  if (!ignore_cache) {
    const cached = Cache.get('settings');
    if (cached) return cached;
  }
  const setting = await request(API.settings);
  Cache.set('settings', setting);
  return setting;
}

// 获取推荐展览
async function getExhiRcmd(ignore_cache = false) {
  if (!ignore_cache) {
    const cached = Cache.get('exhi_rcmd');
    if (cached) return cached;
  }
  const data = await request(API.exhiRcmd);
  if (data.rcmds) {
    Cache.set('exhi_rcmd', data, API_EXPIR);
    return data.rcmds;
  } else {
    return [];
  }
}

// 获取展览列表
async function getTicketList(city = null, page = 1) {
  const params = { page };
  if (city) params.city = city;

  const data = await request(API.ticketList, params);
  // 对列表中的每个展览进行缓存
  if (data.tickets) {
    data.tickets.forEach((ticket) => {
      Cache.set(`ticket-${ticket.id}`, ticket, API_EXPIR);
    });
    return data.tickets;
  } else {
    return [];
  }
}

// 获取展览详情
async function getTicketDetail(tid, ignore_cache = false) {
  if (!ignore_cache) {
    const cached = Cache.get(`ticket-${tid}`);
    if (cached) return cached;
  }
  const data = await request(API.ticketDetail, { tid });
  Cache.set(`ticket-${tid}`, data, API_EXPIR);
  return data;
}

// 获取展览票价
async function getTicketPrice(tid, ignore_cache = false) {
  if (!ignore_cache) {
    const cached = Cache.get(`ticketPrice-${tid}`);
    if (cached) return cached;
  }
  const data = await request(API.ticketPrice, { tid });
  Cache.set(`ticketPrice-${tid}`, data, API_EXPIR);
  return data;
}

// 获取联票列表
async function getJointList(city = null, page = 1) {
  const params = { page };
  if (city) params.city = city;

  const data = await request(API.jointList, params);
  // 对列表中的每个联票进行缓存
  if (data.joints) {
    data.joints.forEach((joint) => {
      Cache.set(`joint-${joint.id}`, joint, API_EXPIR);
    });
    return data.joints;
  } else {
    return [];
  }
}

// 获取联票详情
async function getJointDetail(jid, ignore_cache = false) {
  if (!ignore_cache) {
    const cached = Cache.get(`joint-${jid}`);
    if (cached) return cached;
  }
  const data = await request(API.jointDetail, { jid });
  Cache.set(`joint-${jid}`, data, API_EXPIR);
  return data;
}

// 获取联票价格
async function getJointPrice(jid, ignore_cache = false) {
  if (!ignore_cache) {
    const cached = Cache.get(`jointPrice-${jid}`);
    if (cached) return cached;
  }
  const data = await request(API.jointPrice, { jid });
  Cache.set(`jointPrice-${jid}`, data, API_EXPIR);
  return data;
}

// 获取预约票列表
async function getReserveList() {
  const params = {};

  const data = await request(API.reserveList, params);
  // 对列表中的每个展览进行缓存
  if (data.tickets) {
    data.tickets.forEach((ticket) => {
      Cache.set(`ticket-${ticket.id}`, ticket, API_EXPIR);
    });
    return data.tickets;
  } else {
    return [];
  }
}

// 获取预约票价
async function getReservePrice(tid, ignore_cache = false) {
  if (!ignore_cache) {
    const cached = Cache.get(`ticketPrice-${tid}`);
    if (cached) return cached;
  }
  const data = await request(API.reservePrice, { tid });
  Cache.set(`ticketPrice-${tid}`, data, API_EXPIR);
  return data;
}

// 确认预约
async function confirmReserve(orderId, date) {
  const params = { order_id: orderId, date: date };
  await request(API.reserveConfirm, params, 'POST');
}

// 提交订单 （POST）
async function submitOrder(tid, catg, quantity, amount, timeslot, grade) {
  const params = { tid, catg, quantity, amount, timeslot, grade };
  return await request(API.orderSubmit, params, 'POST');
}

// 从订单页继续支付
async function continue2Pay(orderId) {
  const data = await request(API.continue2Pay, { order_id: orderId }, 'GET');
  return data;
}

// 订单列表
async function getOrderList(is_joint = false, status = null, page = 1) {
  const params = { is_joint, page };
  if (status) params.status = status;
  return (await request(API.orderList, params)) || [];
}

// 核销门票
async function checkinOrder(vcode) {
  return await request(API.orderCheckin, { vcode: vcode }, 'POST');
}

// 订单详情
async function getOrderDetail(orderId) {
  return await request(API.orderDetail, { order_id: orderId });
}

// 取消订单 （POST）
async function cancelOrder(orderId) {
  request(API.orderCancel, { order_id: orderId }, 'POST').then((data) => {
    wx.showToast({ title: data.msg, icon: 'success' });
  });
}

// 申请退款 （POST）
async function refundOrder(orderId) {
  request(API.orderRefund, { order_id: orderId }, 'POST').then((data) => {
    wx.showToast({ title: data.msg, icon: 'success' });
  });
}

// 删除订单 （DELETE）
async function deleteOrder(orderId) {
  request(API.orderDelete, { order_id: orderId }, 'DELETE').then((data) => {
    wx.showToast({ title: data.msg, icon: 'success' });
  });
}

// 导出所有接口
export default {
  login,
  getProfile,
  getUserPhone,
  bindUserPhone,
  getSettings,
  getExhiRcmd,
  getTicketList,
  getTicketDetail,
  getTicketPrice,
  getJointList,
  getJointDetail,
  getJointPrice,
  getReserveList,
  getReservePrice,
  confirmReserve,
  submitOrder,
  continue2Pay,
  getOrderList,
  checkinOrder,
  getOrderDetail,
  cancelOrder,
  refundOrder,
  deleteOrder
};
