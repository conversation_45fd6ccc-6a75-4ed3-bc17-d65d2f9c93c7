import api from '../../../utils/http';

Page({
  // 页面的初始数据
  data: {
    jointDetail: {},
    features: {
      refundable: false, // 是否可退
      source: 'office', // 票源
      electronic: true, // 是否电子票
      invoice: `需要开具发票的购票用户，请您在演出开始5日前，电话联系客服（40008822）并提供开票信息，\
                展期结束后将统-由演出主办单位开具` // 发票信息
    },
    showNoticeDialog: false // 控制通知弹窗显示
  },

  onLoad(options) {
    api.getJointDetail(options.id).then((joint) => {
      if (!joint) {
        wx.showToast({ title: '服务器维护中，请稍候再试', icon: 'none' });
        return;
      }

      // 保存数据、设置导航栏标题
      this.setData({ jointDetail: joint });

      // 如果notice不为空，显示通知弹窗
      if (joint.notice) {
        this.setData({ showNoticeDialog: true });
      }
    });
  },

  // 处理通知弹窗确认事件
  onNoticeDialogConfirm: function () {
    this.setData({
      showNoticeDialog: false
    });
  },

  // 导航到特征详情页
  goToFeatures: function (e) {
    wx.navigateTo({ url: '/pages/ticket/features/features' });
  },

  // 导航到关联展票的详情页
  goToTicketDetail: function (e) {
    const ticketId = e.currentTarget.dataset.id;
    wx.navigateTo({ url: `/pages/ticket/exhib/exhib?id=${ticketId}` });
  },

  // 购票按钮点击事件
  goToJointPurchase: function () {
    const jk = this.data.jointDetail;
    wx.navigateTo({
      url: `/pages/order/purchase/purchase?id=${jk.id}&title=${jk.title}&type=joint`
    });
  },

  // AR按钮点击事件
  goToTicketAR: function () {
    // AR相关逻辑
    if (!this.data.ticketDetail) return;
    wx.showToast({
      title: 'AR功能开发中，敬请期待',
      icon: 'none'
    });
  }
});
