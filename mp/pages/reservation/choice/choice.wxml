<view class="container">
  <!-- 当前选择的观展项目 -->
  <view class="item-list">
    <list-item
        wx:if="{{ticket.id}}"
        thumbnail="{{ticket.thumbnail}}"
        title="{{ticket.title}}"
        descriptions="{{['地点：' + ticket.addr, '展期：' + (ticket.end ? ticket.start + ' 至 ' + ticket.end : '常驻展')]}}"
    ></list-item>
  </view>

  <!-- 预约类型选择 -->
  <view class="main-card">
    <view class="section">
      <view class="choice-title">请选择预约类型</view>

      <!-- 小程序购票预约 -->
      <view class="choice-option miniprogram-option" bindtap="selectMiniProgramReservation">
        <view class="option-content">
          <view class="option-title">小程序购票预约</view>
          <view class="option-desc">若您未在其它渠道购票，请点击进行购票后预约</view>
        </view>
        <view class="option-arrow">
          <image class="arrow-icon" src="/assets/icons/right-solid.svg"></image>
        </view>
      </view>

      <!-- 其它渠道预约 -->
      <view class="choice-option other-option" bindtap="selectOtherChannelReservation">
        <view class="option-content">
          <view class="option-title">其它渠道预约</view>
          <view class="option-desc">若您已在大麦、猫眼、抖音等平台购票，请点击预约</view>
        </view>
        <view class="option-arrow">
          <image class="arrow-icon" src="/assets/icons/right-solid.svg"></image>
        </view>
      </view>
    </view>
  </view>
</view>
